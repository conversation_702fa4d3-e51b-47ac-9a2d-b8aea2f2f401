---
type: "always_apply"
description: "globs:"
---
# WinForm 开发规则

您是一位资深的 .NET 后端开发人员，并且是 C#、WinForm、SQL Server、ADO.Net 方面的专家。

## 代码风格和结构
- 编写简洁、地道的 C# 代码，并提供准确的示例。
- 酌情使用面向对象和函数式编程模式。
- 优先使用 LINQ 和 lambda 表达式进行集合操作。
- 使用描述性的变量和方法名（例如，`IsUserSignedIn`、`CalculateTotal`）。

## 命名约定
- 类名、方法名和公共成员使用帕斯卡命名法 (PascalCase)。
- 局部变量和私有字段使用驼峰命名法 (camelCase)。
- 常量使用全大写字母 (UPPERCASE)。
- 接口名称以 "I" 为前缀（例如，`IUserService`）。

## C# 和 .NET 用法
- 开发使用.Net Framework4的语法特性

## 开发细节
- 窗体全部继承Common.BaseForm的窗体，第一个的BaseFather，如果是ShowDialog出的窗体继承BaseChild
- 使用TableLayout进行控件布局，基本控件使用来自CustomControl中的控件
- TextBox控件使用CustomControl的MyTextBox，Grid使用MyGrid，数值型控件使用MyNumericEdit
- 数据库连接使用Ado.Net,三层架构BLL,Model,IDAL,SqlServerDAL
- 枚举封装成控件使用，Designer.cs里的Captain改成枚举的中文含义，示例
  ```c#
    public partial class SingleAbnormalFlag : MySingleComobo
    {
        public SingleAbnormalFlag()
        {
            InitializeComponent();
        }
  
        protected override void OnPaint(PaintEventArgs pe)
        {
            base.OnPaint(pe);
        }
  
        public void Init()
        {
            DataSource = typeof(AbnormalFlag);
            DisplayColumns[1].Visible = false;
            DroupDownWidth = Width - (int)CaptainWidth;
            SelectedIndex = 0;
            ItemHeight = 20;
        }
    }
  ```

- 包含启用禁用状态列放到第一列

- 不要添加CustomControl中的控件没有的属性

- 如果我叫你仿照某个窗体开发一个新的窗体时，如果我没要求，你不要自行创建被仿照的窗体没有的函数和控件属性

- 参考窗体新做窗体时不要添加参考窗体的Designer.cs文件没有的属性，也不要添加控件没有的属性，要同时创建resx文件

- Designer.cs的控件初始化全写到InitializeComponent，不要另创方法，一定要符合标准的Designer文件格式

- TableLayoutPanel的行高一般是29，控件的字体是宋体, 10.5pt

- MyTextbox，MyDateEdit，MyNumericEdit，MyDtComobo的dock属性改成none,anchor改成right,left
## 控件命名

- MyDateEdit以Date开头加字段名命名
- MyDtComobo以Dtcbo开头加字段名命名
- MyNumericEdit以Num开头加字段名命名
- MyTextBox以Txt开头加字段名命名
- MyButton以Btn加功能英文命名
- 检测控件是否必填以这种代码方式

  ```c#
  if (CustomControl.Func.NotAllowEmpty(cboInstrument)) return false;
  ```
- 大部分CustomControl里的输入控件都有Captain属性，这个属性是标题，创建控件时，不用再加Label作为标题

* 控件的添加一定要放到Designer文件里
