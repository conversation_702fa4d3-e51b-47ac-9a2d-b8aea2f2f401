using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using BLL;
using Model;
using YdVar;
using Common.BaseForm;

namespace YdSysManage
{
    public partial class Sysconfig : BaseFather
    {
        private BLL.BllZd_Cs _bllZd_Cs = new BllZd_Cs();
        private Model.MdlZd_Cs _mdlZd_Cs = new MdlZd_Cs();

        public Sysconfig()
        {
            InitializeComponent();
            this.Load += Sysconfig_Load;
        }

        private void Sysconfig_Load(object sender, EventArgs e)
        {
            InitializeControls();
            LoadData();
        }

        private void InitializeControls()
        {
            // 控件已在Designer中创建，这里可以做额外的初始化
        }

        private void LoadData()
        {
            try
            {
                // 获取当前药店的参数设置
                string ydCode = YdVar.Var.Yd_Code;
                _mdlZd_Cs = _bllZd_Cs.GetModel(ydCode);

                if (_mdlZd_Cs == null)
                {
                    // 如果没有记录，创建新的
                    _mdlZd_Cs = new MdlZd_Cs();
                    _mdlZd_Cs.Yd_Code = ydCode;
                }

                // 填充数据到控件
                FillDataToControls();
            }
            catch (Exception ex)
            {
                MessageBox.Show("加载数据失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FillDataToControls()
        {
            TxtYdCode.Text = _mdlZd_Cs.Yd_Code ?? "";
            TxtAppKey.Text = _mdlZd_Cs.appkey ?? "";
            TxtSecret.Text = _mdlZd_Cs.secret ?? "";
            TxtRefEntId.Text = _mdlZd_Cs.RefEntId ?? "";
        }

        private Control FindControlByName(string name)
        {
            return FindControlByName(tabPage1, name);
        }

        private Control FindControlByName(Control parent, string name)
        {
            if (parent.Name == name)
                return parent;

            foreach (Control child in parent.Controls)
            {
                Control found = FindControlByName(child, name);
                if (found != null)
                    return found;
            }
            return null;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证数据
                if (!ValidateData())
                    return;

                // 获取控件数据
                GetDataFromControls();

                // 保存数据
                bool result;
                if (_bllZd_Cs.Exists(_mdlZd_Cs.Yd_Code))
                {
                    result = _bllZd_Cs.Update(_mdlZd_Cs);
                }
                else
                {
                    result = _bllZd_Cs.Add(_mdlZd_Cs);
                }

                if (result)
                {
                    MessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("保存失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("保存失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnTest_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取控件数据
                GetDataFromControls();

                // 这里可以添加测试连接的逻辑
                // 例如调用码上放心的API进行测试
                MessageBox.Show("测试连接功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("测试连接失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateData()
        {
            if (CustomControl.Func.NotAllowEmpty(TxtYdCode)) return false;
            if (CustomControl.Func.NotAllowEmpty(TxtAppKey)) return false;
            if (CustomControl.Func.NotAllowEmpty(TxtSecret)) return false;
            if (CustomControl.Func.NotAllowEmpty(TxtRefEntId)) return false;

            return true;
        }

        private void GetDataFromControls()
        {
            _mdlZd_Cs.Yd_Code = TxtYdCode.Text.Trim();
            _mdlZd_Cs.appkey = TxtAppKey.Text.Trim();
            _mdlZd_Cs.secret = TxtSecret.Text.Trim();
            _mdlZd_Cs.RefEntId = TxtRefEntId.Text.Trim();
        }
    }
}
