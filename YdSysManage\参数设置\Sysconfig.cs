using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using BLL;
using Model;
using YdVar;
using Common.BaseForm;

namespace YdSysManage
{
    public partial class Sysconfig : BaseFather
    {
        private BLL.BllZd_Cs _bllZd_Cs = new BllZd_Cs();
        private Model.MdlZd_Cs _mdlZd_Cs = new MdlZd_Cs();

        public Sysconfig()
        {
            InitializeComponent();
            this.Load += Sysconfig_Load;
        }

        private void Sysconfig_Load(object sender, EventArgs e)
        {
            InitializeControls();
            LoadData();
        }

        private void InitializeControls()
        {
            // 创建控件
            CreateControls();
        }

        private void CreateControls()
        {
            // 清空tabPage1的控件
            tabPage1.Controls.Clear();

            // 创建TableLayoutPanel
            TableLayoutPanel tableLayout = new TableLayoutPanel();
            tableLayout.Dock = DockStyle.Fill;
            tableLayout.ColumnCount = 2;
            tableLayout.RowCount = 5;
            tableLayout.Padding = new Padding(20);

            // 设置列宽
            tableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            tableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 设置行高
            for (int i = 0; i < 5; i++)
            {
                tableLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));
            }

            // 药店编码
            Label lblYdCode = new Label();
            lblYdCode.Text = "药店编码：";
            lblYdCode.TextAlign = ContentAlignment.MiddleRight;
            lblYdCode.Font = new Font("宋体", 10.5f);
            lblYdCode.Dock = DockStyle.Fill;

            CustomControl.MyTextBox txtYdCode = new CustomControl.MyTextBox();
            txtYdCode.Name = "TxtYdCode";
            txtYdCode.Captain = "药店编码";
            txtYdCode.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            txtYdCode.Font = new Font("宋体", 10.5f);
            txtYdCode.MaxLength = 4;

            // AppKey
            Label lblAppKey = new Label();
            lblAppKey.Text = "AppKey：";
            lblAppKey.TextAlign = ContentAlignment.MiddleRight;
            lblAppKey.Font = new Font("宋体", 10.5f);
            lblAppKey.Dock = DockStyle.Fill;

            CustomControl.MyTextBox txtAppKey = new CustomControl.MyTextBox();
            txtAppKey.Name = "TxtAppKey";
            txtAppKey.Captain = "AppKey";
            txtAppKey.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            txtAppKey.Font = new Font("宋体", 10.5f);
            txtAppKey.MaxLength = 50;

            // Secret
            Label lblSecret = new Label();
            lblSecret.Text = "Secret：";
            lblSecret.TextAlign = ContentAlignment.MiddleRight;
            lblSecret.Font = new Font("宋体", 10.5f);
            lblSecret.Dock = DockStyle.Fill;

            CustomControl.MyTextBox txtSecret = new CustomControl.MyTextBox();
            txtSecret.Name = "TxtSecret";
            txtSecret.Captain = "Secret";
            txtSecret.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            txtSecret.Font = new Font("宋体", 10.5f);
            txtSecret.MaxLength = 200;

            // RefEntId
            Label lblRefEntId = new Label();
            lblRefEntId.Text = "企业ID：";
            lblRefEntId.TextAlign = ContentAlignment.MiddleRight;
            lblRefEntId.Font = new Font("宋体", 10.5f);
            lblRefEntId.Dock = DockStyle.Fill;

            CustomControl.MyTextBox txtRefEntId = new CustomControl.MyTextBox();
            txtRefEntId.Name = "TxtRefEntId";
            txtRefEntId.Captain = "企业ID";
            txtRefEntId.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            txtRefEntId.Font = new Font("宋体", 10.5f);
            txtRefEntId.MaxLength = 200;

            // 按钮面板
            Panel buttonPanel = new Panel();
            buttonPanel.Height = 35;
            buttonPanel.Dock = DockStyle.Fill;

            CustomControl.MyButton btnSave = new CustomControl.MyButton();
            btnSave.Name = "BtnSave";
            btnSave.Text = "保存";
            btnSave.Size = new Size(80, 29);
            btnSave.Location = new Point(0, 3);
            btnSave.Font = new Font("宋体", 10.5f);
            btnSave.Click += BtnSave_Click;

            CustomControl.MyButton btnTest = new CustomControl.MyButton();
            btnTest.Name = "BtnTest";
            btnTest.Text = "测试连接";
            btnTest.Size = new Size(80, 29);
            btnTest.Location = new Point(90, 3);
            btnTest.Font = new Font("宋体", 10.5f);
            btnTest.Click += BtnTest_Click;

            buttonPanel.Controls.Add(btnSave);
            buttonPanel.Controls.Add(btnTest);

            // 添加控件到TableLayout
            tableLayout.Controls.Add(lblYdCode, 0, 0);
            tableLayout.Controls.Add(txtYdCode, 1, 0);
            tableLayout.Controls.Add(lblAppKey, 0, 1);
            tableLayout.Controls.Add(txtAppKey, 1, 1);
            tableLayout.Controls.Add(lblSecret, 0, 2);
            tableLayout.Controls.Add(txtSecret, 1, 2);
            tableLayout.Controls.Add(lblRefEntId, 0, 3);
            tableLayout.Controls.Add(txtRefEntId, 1, 3);
            tableLayout.Controls.Add(buttonPanel, 1, 4);

            tabPage1.Controls.Add(tableLayout);
        }

        private void LoadData()
        {
            try
            {
                // 获取当前药店的参数设置
                string ydCode = YdVar.Var.Yd_Code;
                _mdlZd_Cs = _bllZd_Cs.GetModel(ydCode);

                if (_mdlZd_Cs == null)
                {
                    // 如果没有记录，创建新的
                    _mdlZd_Cs = new MdlZd_Cs();
                    _mdlZd_Cs.Yd_Code = ydCode;
                }

                // 填充数据到控件
                FillDataToControls();
            }
            catch (Exception ex)
            {
                MessageBox.Show("加载数据失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FillDataToControls()
        {
            CustomControl.MyTextBox txtYdCode = FindControlByName("TxtYdCode") as CustomControl.MyTextBox;
            CustomControl.MyTextBox txtAppKey = FindControlByName("TxtAppKey") as CustomControl.MyTextBox;
            CustomControl.MyTextBox txtSecret = FindControlByName("TxtSecret") as CustomControl.MyTextBox;
            CustomControl.MyTextBox txtRefEntId = FindControlByName("TxtRefEntId") as CustomControl.MyTextBox;

            if (txtYdCode != null) txtYdCode.Text = _mdlZd_Cs.Yd_Code ?? "";
            if (txtAppKey != null) txtAppKey.Text = _mdlZd_Cs.appkey ?? "";
            if (txtSecret != null) txtSecret.Text = _mdlZd_Cs.secret ?? "";
            if (txtRefEntId != null) txtRefEntId.Text = _mdlZd_Cs.RefEntId ?? "";
        }

        private Control FindControlByName(string name)
        {
            return FindControlByName(tabPage1, name);
        }

        private Control FindControlByName(Control parent, string name)
        {
            if (parent.Name == name)
                return parent;

            foreach (Control child in parent.Controls)
            {
                Control found = FindControlByName(child, name);
                if (found != null)
                    return found;
            }
            return null;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证数据
                if (!ValidateData())
                    return;

                // 获取控件数据
                GetDataFromControls();

                // 保存数据
                bool result;
                if (_bllZd_Cs.Exists(_mdlZd_Cs.Yd_Code))
                {
                    result = _bllZd_Cs.Update(_mdlZd_Cs);
                }
                else
                {
                    result = _bllZd_Cs.Add(_mdlZd_Cs);
                }

                if (result)
                {
                    MessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("保存失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("保存失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnTest_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取控件数据
                GetDataFromControls();

                // 这里可以添加测试连接的逻辑
                // 例如调用码上放心的API进行测试
                MessageBox.Show("测试连接功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("测试连接失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateData()
        {
            CustomControl.MyTextBox txtYdCode = FindControlByName("TxtYdCode") as CustomControl.MyTextBox;
            CustomControl.MyTextBox txtAppKey = FindControlByName("TxtAppKey") as CustomControl.MyTextBox;
            CustomControl.MyTextBox txtSecret = FindControlByName("TxtSecret") as CustomControl.MyTextBox;
            CustomControl.MyTextBox txtRefEntId = FindControlByName("TxtRefEntId") as CustomControl.MyTextBox;

            if (CustomControl.Func.NotAllowEmpty(txtYdCode)) return false;
            if (CustomControl.Func.NotAllowEmpty(txtAppKey)) return false;
            if (CustomControl.Func.NotAllowEmpty(txtSecret)) return false;
            if (CustomControl.Func.NotAllowEmpty(txtRefEntId)) return false;

            return true;
        }

        private void GetDataFromControls()
        {
            CustomControl.MyTextBox txtYdCode = FindControlByName("TxtYdCode") as CustomControl.MyTextBox;
            CustomControl.MyTextBox txtAppKey = FindControlByName("TxtAppKey") as CustomControl.MyTextBox;
            CustomControl.MyTextBox txtSecret = FindControlByName("TxtSecret") as CustomControl.MyTextBox;
            CustomControl.MyTextBox txtRefEntId = FindControlByName("TxtRefEntId") as CustomControl.MyTextBox;

            if (txtYdCode != null) _mdlZd_Cs.Yd_Code = txtYdCode.Text.Trim();
            if (txtAppKey != null) _mdlZd_Cs.appkey = txtAppKey.Text.Trim();
            if (txtSecret != null) _mdlZd_Cs.secret = txtSecret.Text.Trim();
            if (txtRefEntId != null) _mdlZd_Cs.RefEntId = txtRefEntId.Text.Trim();
        }
    }
}
