-- 数据库更新脚本 - 20250801
-- 功能：添加 Yk_Rk1.Rk_Ok 列，创建 Yk_Rk2 和 Yk_RkDrugtracinfo 表

-- OSQL.EXE 兼容版本
SET NOCOUNT ON
GO

BEGIN TRANSACTION
GO

-- 1. 给 Yk_Rk1 表添加 Rk_Ok 列
IF COL_LENGTH('dbo.Yk_Rk1', 'Rk_Ok') IS NULL
BEGIN
    ALTER TABLE dbo.Yk_Rk1 ADD Rk_Ok VARCHAR(50) DEFAULT '未完成'
    PRINT '已成功添加 Yk_Rk1.Rk_Ok 列'
END
ELSE
BEGIN
    PRINT 'Yk_Rk1.Rk_Ok 列已存在，跳过添加'
END
GO


-- 修改 Zd_Yp3 表结构
IF COL_LENGTH('dbo.Zd_Yp3', 'Rk_Dj') IS NULL
BEGIN
    ALTER TABLE dbo.Zd_Yp3 ADD Rk_Dj [numeric](18, 6) DEFAULT 0
    PRINT '已成功添加 Zd_Yp3.Rk_Dj 列'
END
ELSE
BEGIN
    PRINT 'Zd_Yp3.Rk_Dj 列已存在，跳过添加'
END
GO

-- 修改 Zd_Yp3.Yp_Count 列类型（先删除约束，再修改列类型，最后重新添加约束）
DECLARE @ConstraintName NVARCHAR(200)
DECLARE @SQL NVARCHAR(1000)

-- 查找并删除 Yp_Count 列的默认约束
SELECT @ConstraintName = dc.name
FROM sys.default_constraints dc
    INNER JOIN sys.columns c ON dc.parent_column_id = c.column_id
    INNER JOIN sys.tables t ON dc.parent_object_id = t.object_id
WHERE t.name = 'Zd_Yp3' AND c.name = 'Yp_Count'

IF @ConstraintName IS NOT NULL
BEGIN
    SET @SQL = 'ALTER TABLE dbo.Zd_Yp3 DROP CONSTRAINT [' + @ConstraintName + ']'
    EXEC sp_executesql @SQL
    PRINT '已删除约束: ' + @ConstraintName
END

-- 修改列类型
ALTER TABLE dbo.Zd_Yp3 ALTER COLUMN Yp_Count [numeric](12, 4)
PRINT '已成功修改 Zd_Yp3.Yp_Count 列类型为 numeric(12, 4)'

-- 重新添加默认约束
ALTER TABLE dbo.Zd_Yp3 ADD CONSTRAINT [DF_Zd_Yp3_Yp_Count] DEFAULT ((0)) FOR [Yp_Count]
PRINT '已重新添加 Zd_Yp3.Yp_Count 默认约束'
GO

-- 2. 创建 Yk_Rk2 表
IF OBJECT_ID('dbo.Yk_Rk2', 'U') IS NULL
BEGIN
    CREATE TABLE [dbo].[Yk_Rk2]
    (
        [Rk_Id] [int] IDENTITY(1,1) NOT NULL,
        [Rk_Code] [char](9) NOT NULL,
        [Xl_Code] [char](8) NOT NULL,
        [Yp_Code] [char](11) NULL,
        [Yp_Scph] [varchar](20) NULL,
        [Yp_ScDate1] [smalldatetime] NULL,
        [Yp_ScDate2] [smalldatetime] NULL,
        [Rk_Sl] [numeric](12, 4) NULL,
        [Rk_Dj] [numeric](18, 6) NULL,
        [Rk_Money] [numeric](18, 4) NULL,
        [Rk_Memo] [varchar](50) NULL,
        CONSTRAINT [PK_Yk_Rk2] PRIMARY KEY CLUSTERED ([Rk_Id] ASC)
            WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF,
                  ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
    ) ON [PRIMARY]

    PRINT '已成功创建 Yk_Rk2 表'
END
ELSE
BEGIN
    PRINT 'Yk_Rk2 表已存在，跳过创建'
END
GO

-- 添加 Yk_Rk2 表的默认约束
IF OBJECT_ID('dbo.Yk_Rk2', 'U') IS NOT NULL
BEGIN
    IF NOT EXISTS (SELECT 1
    FROM sys.default_constraints
    WHERE name = 'DF_Yk_Rk2_Rk_Sl')
        ALTER TABLE [dbo].[Yk_Rk2] ADD CONSTRAINT [DF_Yk_Rk2_Rk_Sl] DEFAULT ((0)) FOR [Rk_Sl]

    IF NOT EXISTS (SELECT 1
    FROM sys.default_constraints
    WHERE name = 'DF_Yk_Rk2_Rk_Dj')
        ALTER TABLE [dbo].[Yk_Rk2] ADD CONSTRAINT [DF_Yk_Rk2_Rk_Dj] DEFAULT ((0)) FOR [Rk_Dj]

    IF NOT EXISTS (SELECT 1
    FROM sys.default_constraints
    WHERE name = 'DF_Yk_Rk2_Rk_Money')
        ALTER TABLE [dbo].[Yk_Rk2] ADD CONSTRAINT [DF_Yk_Rk2_Rk_Money] DEFAULT ((0)) FOR [Rk_Money]
END
GO

-- 添加 Yk_Rk2 表的列注释
IF OBJECT_ID('dbo.Yk_Rk2', 'U') IS NOT NULL
BEGIN
    EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_数量',
        @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Rk2',
        @level2type=N'COLUMN',@level2name=N'Rk_Sl'

    EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_单价',
        @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Rk2',
        @level2type=N'COLUMN',@level2name=N'Rk_Dj'

    EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_金额',
        @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Rk2',
        @level2type=N'COLUMN',@level2name=N'Rk_Money'

    EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_备注',
        @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Rk2',
        @level2type=N'COLUMN',@level2name=N'Rk_Memo'
END
GO

-- 3. 创建 Yk_RkDrugtracinfo 表
IF OBJECT_ID('dbo.Yk_RkDrugtracinfo', 'U') IS NULL
BEGIN
    CREATE TABLE [dbo].[Yk_RkDrugtracinfo]
    (
        [Rk_Id] [int] NOT NULL,
        [drug_trac_codg] [varchar](100) NOT NULL,
        CONSTRAINT [PK_Yk_Rk_Drugtracinfo] PRIMARY KEY CLUSTERED ([Rk_Id] ASC, [drug_trac_codg] ASC)
            WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF,
                  ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
    ) ON [PRIMARY]

    PRINT '已成功创建 Yk_RkDrugtracinfo 表'
END
ELSE
BEGIN
    PRINT 'Yk_RkDrugtracinfo 表已存在，跳过创建'
END
GO

-- 提交事务
COMMIT TRANSACTION
PRINT '数据库更新成功完成！'
GO
